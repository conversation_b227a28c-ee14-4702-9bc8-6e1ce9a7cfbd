import { render, screen, fireEvent, waitFor } from '@testing-library/react';
import { toast } from 'sonner';
import { EditServiceModal } from '../edit-service-modal';
import { useServiceStore } from '@/store/service/action';

// Mock the service store
jest.mock('@/store/service/action');
jest.mock('sonner');

const mockService = {
  id: 1,
  name: 'test-service',
  port: '80',
  target_port: '8080',
  type: 'ClusterIP',
  cluster_ip: '********',
  external_ip: null,
  namespace_id: 1,
  deployment_id: 1,
  created_at: '2023-01-01T00:00:00Z',
  updated_at: '2023-01-01T00:00:00Z',
  status: { id: 1, name: 'Running' },
  ingress_specs: [],
};

const mockUseServiceStore = useServiceStore as jest.MockedFunction<typeof useServiceStore>;
const mockToast = toast as jest.Mocked<typeof toast>;

describe('EditServiceModal', () => {
  const mockUpdateService = jest.fn();
  const mockOnOpenChange = jest.fn();
  const mockOnSuccess = jest.fn();

  beforeEach(() => {
    mockUseServiceStore.mockReturnValue({
      updateService: mockUpdateService,
      updating: false,
      services: [],
      selectedService: null,
      loading: false,
      creating: false,
      deleting: false,
      setServices: jest.fn(),
      setSelectedService: jest.fn(),
      setLoading: jest.fn(),
      setCreating: jest.fn(),
      setUpdating: jest.fn(),
      setDeleting: jest.fn(),
      fetchServices: jest.fn(),
      fetchService: jest.fn(),
      createService: jest.fn(),
      deleteService: jest.fn(),
    });
    
    mockUpdateService.mockClear();
    mockOnOpenChange.mockClear();
    mockOnSuccess.mockClear();
    mockToast.success.mockClear();
    mockToast.error.mockClear();
  });

  it('renders the modal with service data', () => {
    render(
      <EditServiceModal
        service={mockService}
        open={true}
        onOpenChange={mockOnOpenChange}
        onSuccess={mockOnSuccess}
      />
    );

    expect(screen.getByText('Edit Service')).toBeInTheDocument();
    expect(screen.getByDisplayValue('80')).toBeInTheDocument();
    expect(screen.getByDisplayValue('8080')).toBeInTheDocument();
    expect(screen.getByDisplayValue('ClusterIP')).toBeInTheDocument();
    expect(screen.getByText('test-service')).toBeInTheDocument();
  });

  it('shows read-only service name', () => {
    render(
      <EditServiceModal
        service={mockService}
        open={true}
        onOpenChange={mockOnOpenChange}
        onSuccess={mockOnSuccess}
      />
    );

    expect(screen.getByText('Service Name (Read Only)')).toBeInTheDocument();
    expect(screen.getByText('test-service')).toBeInTheDocument();
  });

  it('allows editing only port, target_port, and type fields', () => {
    render(
      <EditServiceModal
        service={mockService}
        open={true}
        onOpenChange={mockOnOpenChange}
        onSuccess={mockOnSuccess}
      />
    );

    const portInput = screen.getByDisplayValue('80');
    const targetPortInput = screen.getByDisplayValue('8080');
    
    expect(portInput).not.toBeDisabled();
    expect(targetPortInput).not.toBeDisabled();
    
    // Service name should be read-only (not an input field)
    expect(screen.queryByDisplayValue('test-service')).not.toBeInTheDocument();
  });

  it('submits the form with only allowed fields', async () => {
    mockUpdateService.mockResolvedValue({ status: true });

    render(
      <EditServiceModal
        service={mockService}
        open={true}
        onOpenChange={mockOnOpenChange}
        onSuccess={mockOnSuccess}
      />
    );

    const portInput = screen.getByDisplayValue('80');
    const targetPortInput = screen.getByDisplayValue('8080');
    const submitButton = screen.getByText('Update Service');

    fireEvent.change(portInput, { target: { value: '443' } });
    fireEvent.change(targetPortInput, { target: { value: '8443' } });
    fireEvent.click(submitButton);

    await waitFor(() => {
      expect(mockUpdateService).toHaveBeenCalledWith(1, {
        port: '443',
        target_port: '8443',
        type: 'ClusterIP',
      });
    });

    expect(mockToast.success).toHaveBeenCalledWith('Service updated successfully');
    expect(mockOnOpenChange).toHaveBeenCalledWith(false);
    expect(mockOnSuccess).toHaveBeenCalled();
  });

  it('handles update errors', async () => {
    mockUpdateService.mockRejectedValue(new Error('Update failed'));

    render(
      <EditServiceModal
        service={mockService}
        open={true}
        onOpenChange={mockOnOpenChange}
        onSuccess={mockOnSuccess}
      />
    );

    const submitButton = screen.getByText('Update Service');
    fireEvent.click(submitButton);

    await waitFor(() => {
      expect(mockToast.error).toHaveBeenCalledWith('Failed to update service');
    });

    expect(mockOnSuccess).not.toHaveBeenCalled();
  });
});
